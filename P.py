#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚀 股票涨停预测系统 P.py - 整理版
基于深度学习和技术分析的A股涨停预测模型

版本：v4.0 (整理版)
更新时间：2025-08-02
作者：AI Assistant
"""

# ==================== 导入区域 ====================

# 标准库导入
import os
import sys
import warnings
import logging
import re
import pickle
import shutil
import inspect
import functools
from functools import wraps, partial
import threading
import traceback
import time
import random
import string
import glob
import math
import json
import uuid
import hashlib
import sqlite3
import gc
import ast
import urllib.parse
import operator
from datetime import datetime, timedelta
from pprint import pformat
from typing import Tuple, Dict, List, Optional, Union, Any
from dataclasses import dataclass
from concurrent.futures import ProcessPoolExecutor, as_completed, TimeoutError

# 科学计算和数据处理
import numpy as np
import pandas as pd

# 机器学习库
from sklearn.preprocessing import StandardScaler, LabelEncoder, RobustScaler
from sklearn.model_selection import train_test_split
from sklearn.feature_selection import SelectKBest, f_classif, mutual_info_classif
from sklearn.neighbors import NearestNeighbors
from sklearn.ensemble import RandomForestClassifier
from sklearn.isotonic import IsotonicRegression
from sklearn.linear_model import LogisticRegression
from sklearn.metrics import (
    roc_curve, precision_recall_curve, auc, accuracy_score,
    precision_score, recall_score, f1_score, mean_absolute_error,
    r2_score, classification_report, confusion_matrix
)

# 高级机器学习库（可选导入）
try:
    from xgboost import XGBClassifier, XGBRegressor
except ImportError:
    XGBClassifier = None
    XGBRegressor = None

try:
    from catboost import CatBoostClassifier, CatBoostRegressor
except ImportError:
    CatBoostClassifier = None
    CatBoostRegressor = None

# 深度学习库
import tensorflow as tf
from tensorflow.keras import backend as K
from tensorflow.keras.optimizers import Adam, AdamW
from tensorflow.keras.layers import Bidirectional
from tensorflow.keras.models import Model
from tensorflow.keras.regularizers import l2
from tensorflow.keras.layers import (
    TimeDistributed, Input, Dense, LayerNormalization, LSTM, BatchNormalization, Dropout,
    Conv1D, Flatten, GRU, MultiHeadAttention, Add, Multiply, GlobalAveragePooling1D,
    Reshape, SpatialDropout1D, GlobalMaxPooling1D, GlobalMaxPool1D, Concatenate, Lambda
)
from tensorflow.keras.callbacks import (
    Callback, ModelCheckpoint, TensorBoard, EarlyStopping,
    ReduceLROnPlateau, TerminateOnNaN, History
)
from tensorflow.keras.metrics import (
    AUC, PrecisionAtRecall, R2Score, BinaryAccuracy, Precision,
    Recall, MeanAbsoluteError, MeanAbsolutePercentageError
)

# 金融数据和技术分析
import tushare as ts
import talib as ta

# 数据可视化
import seaborn as sns
import matplotlib.pyplot as plt

# 优化和采样
import optuna
import optuna.visualization as ov
from imblearn.over_sampling import ADASYN, SMOTE

# 工具库
import requests
import psutil
import joblib
import torch
import questionary
from questionary import Style
from tenacity import retry
from pytz import timezone

# 可选导入（带错误处理）
try:
    from scipy import stats
except ImportError:
    stats = None

try:
    from lightgbm import LGBMClassifier
except ImportError:
    LGBMClassifier = None

try:
    from sklearn.calibration import CalibratedClassifierCV, calibration_curve
except ImportError:
    from sklearn.metrics import calibration_curve
    CalibratedClassifierCV = None

try:
    from innvestigate import create_analyzer
except ImportError:
    create_analyzer = None

# 警告过滤
warnings.filterwarnings('ignore')

# ==================== 全局配置 ====================

@dataclass
class SystemConfig:
    """系统配置类"""
    # 基础配置
    START_DATE: str = '2020-01-01'
    END_DATE: str = None
    LOG_DIR: str = 'logs'
    MODEL_DIR: str = 'models'
    CACHE_DIR: str = 'cache'
    
    # 特征配置
    ENABLE_FEATURE_OPTIMIZATION: bool = False  # 取消特征限制，使用全部特征
    USE_ALL_FEATURES: bool = True  # 使用全部特征
    
    # 模型配置
    DEFAULT_LEARNING_RATE: float = 0.001
    BATCH_SIZE: int = 32
    EPOCHS: int = 100
    PATIENCE: int = 15
    
    # 交易配置
    LIMIT_UP_PCT: float = 0.10  # 10%涨停
    LIMIT_DOWN_PCT: float = -0.10  # 10%跌停
    
    def __post_init__(self):
        if self.END_DATE is None:
            self.END_DATE = datetime.now().strftime('%Y-%m-%d')
        
        # 创建必要目录
        for directory in [self.LOG_DIR, self.MODEL_DIR, self.CACHE_DIR]:
            os.makedirs(directory, exist_ok=True)

@dataclass
class TradingRules:
    """A股交易规则配置"""
    # 涨跌停限制
    LIMIT_UP_PCT: float = 0.10
    LIMIT_DOWN_PCT: float = -0.10
    
    # 交易时间
    MORNING_START: str = "09:30"
    MORNING_END: str = "11:30"
    AFTERNOON_START: str = "13:00"
    AFTERNOON_END: str = "15:00"
    
    # T+1规则
    T_PLUS_ONE: bool = True
    
    # 最小交易单位
    MIN_TRADE_UNIT: int = 100  # 手

@dataclass
class TimeSeriesConfig:
    """时间序列分割配置"""
    discovery_start: str = '2020-01-01'  # 策略发现期开始
    discovery_end: str = '2022-12-31'    # 策略发现期结束
    validation_end: str = '2023-12-31'   # 验证期结束
    test_end: str = None                 # 测试期结束（None表示到今天）

    def __post_init__(self):
        if self.test_end is None:
            self.test_end = datetime.now().strftime('%Y-%m-%d')

# 全局配置实例
Config = SystemConfig()
trading_rules = TradingRules()
time_series_config = TimeSeriesConfig()

# 标准化参数
NORMALIZATION_PARAMS = {
    '首板': {
        'MAIN': {
            'regression_output_1': {'median': 1.0000, 'iqr': 19.2948, 'clip_range': 15.0},
            'regression_output_2': {'median': 1.0000, 'iqr': 8.0000, 'clip_range': 15.0}
        }
    },
    '连板': {
        'MAIN': {
            'regression_output_1': {'median': 1.0000, 'iqr': 15.4950, 'clip_range': 15.0},
            'regression_output_2': {'median': 1.0000, 'iqr': 15.3938, 'clip_range': 15.0}
        }
    }
}

print("🚀 股票涨停预测系统启动")
print(f"📊 特征优化模式: {'禁用' if not Config.ENABLE_FEATURE_OPTIMIZATION else '启用'}")
print(f"🎯 使用全部特征: {'是' if Config.USE_ALL_FEATURES else '否'}")
print(f"📅 数据范围: {Config.START_DATE} ~ {Config.END_DATE}")

# ==================== 工具函数 ====================

def setup_logging(level=logging.INFO):
    """配置日志系统"""
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(os.path.join(Config.LOG_DIR, 'trading_system.log'), encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    # 设置第三方库日志级别
    logging.getLogger('tensorflow').setLevel(logging.WARNING)
    logging.getLogger('matplotlib').setLevel(logging.WARNING)
    
    return logging.getLogger(__name__)

def validate_no_data_leakage(X_train, X_test, y_train, y_test):
    """验证数据分割是否存在泄露"""
    try:
        # 1. 检查时间顺序（如果有时间信息）
        if hasattr(X_train, 'index') and hasattr(X_test, 'index'):
            if hasattr(X_train.index, 'max') and hasattr(X_test.index, 'min'):
                train_end = X_train.index.max()
                test_start = X_test.index.min()
                if train_end > test_start:
                    logging.warning("⚠️ 数据泄露风险：训练集时间晚于测试集")
                    return False

        # 2. 检查数据重叠
        if hasattr(X_train, 'index') and hasattr(X_test, 'index'):
            overlap = set(X_train.index) & set(X_test.index)
            if overlap:
                logging.warning(f"⚠️ 数据泄露风险：训练集和测试集有{len(overlap)}个重叠样本")
                return False

        # 3. 检查标签分布合理性
        if isinstance(y_train, dict) and isinstance(y_test, dict):
            for key in y_train.keys():
                if 'classification' in key:
                    train_pos_rate = y_train[key].mean()
                    test_pos_rate = y_test[key].mean()
                    if abs(train_pos_rate - test_pos_rate) > 0.3:
                        logging.warning(f"⚠️ 标签分布差异过大：{key} 训练集{train_pos_rate:.3f} vs 测试集{test_pos_rate:.3f}")

        logging.info("✅ 数据泄露验证通过")
        return True

    except Exception as e:
        logging.warning(f"数据泄露验证失败: {e}")
        return True  # 验证失败时不阻止训练

# ==================== 数据获取模块 ====================

def simple_api_retry(api_func, api_name, max_retries=3, **kwargs):
    """简单的API重试机制"""
    for attempt in range(max_retries):
        try:
            result = api_func(**kwargs)
            if result is not None and not result.empty:
                return result
            else:
                logging.warning(f"API {api_name} 返回空数据，尝试 {attempt + 1}/{max_retries}")
        except Exception as e:
            logging.error(f"API {api_name} 调用失败 (尝试 {attempt + 1}/{max_retries}): {e}")
            if attempt < max_retries - 1:
                time.sleep(2 ** attempt)  # 指数退避

    logging.error(f"API {api_name} 多次重试后仍然失败")
    return pd.DataFrame()

def fetch_data(ts_codes, start_date=None, end_date=None):
    """批量获取股票的历史数据，并缓存"""
    if start_date is None:
        start_date = Config.START_DATE
    if end_date is None:
        end_date = Config.END_DATE

    logging.info(f'获取股票数据 时间范围: {start_date} - {end_date}')
    data_list = []
    ts_codes_to_fetch = []

    # 检查缓存，找出需要获取的股票代码
    for ts_code in ts_codes:
        cache_path = os.path.join(Config.CACHE_DIR, f'{ts_code}_qfq.csv')
        if os.path.exists(cache_path):
            df = pd.read_csv(cache_path)
            data_list.append(df)
            logging.info(f'从缓存加载 {ts_code} 的数据')
        else:
            ts_codes_to_fetch.append(ts_code)

    if not ts_codes_to_fetch:
        # 所有数据都已缓存，无需再获取
        if data_list:
            df = pd.concat(data_list, ignore_index=True)
            # 向量化涨停判断
            star_chinext_mask = df['ts_code'].str.startswith(('688', '300'))
            limit_threshold = np.where(star_chinext_mask, 19.9, 9.9)
            df['limit_up'] = df['pct_chg'] >= limit_threshold
            return df
        else:
            return pd.DataFrame()

    # 批量获取需要的数据
    batch_size = 500
    ts_codes_str_list = [','.join(ts_codes_to_fetch[i:i + batch_size]) for i in
                         range(0, len(ts_codes_to_fetch), batch_size)]
    max_retries = 3

    for ts_codes_str in ts_codes_str_list:
        for attempt in range(max_retries):
            try:
                # 计算需要获取的日期范围
                start_dates = []
                end_dates = []
                current_date = pd.to_datetime(start_date)
                final_date = pd.to_datetime(end_date)

                # 将时间范围分成多个小区间，每个区间约240个交易日
                while current_date <= final_date:
                    start_dates.append(current_date.strftime('%Y%m%d'))
                    next_date = current_date + pd.DateOffset(days=240)
                    if next_date > final_date:
                        next_date = final_date
                    end_dates.append(next_date.strftime('%Y%m%d'))
                    current_date = next_date + pd.DateOffset(days=1)

                # 对每个时间区间获取数据
                df_list = []
                for s_date, e_date in zip(start_dates, end_dates):
                    # 批量获取日线行情数据
                    df_daily = simple_api_retry(ts.pro_api().daily, "daily", max_retries=3,
                                                ts_code=ts_codes_str,
                                                start_date=s_date,
                                                end_date=e_date,
                                                fields='ts_code,trade_date,open,high,low,close,pre_close,change,pct_chg,vol,amount'
                                                )

                    # 验证关键字段是否存在
                    if df_daily is not None and not df_daily.empty:
                        required_fields = ['ts_code', 'trade_date', 'pct_chg', 'vol', 'amount']
                        missing_fields = [field for field in required_fields if field not in df_daily.columns]
                        if missing_fields:
                            logging.error(f"❌ 关键字段缺失: {missing_fields}")
                            continue

                        # 检查成交额数据质量
                        amount_null_ratio = df_daily['amount'].isnull().sum() / len(df_daily)
                        if amount_null_ratio > 0.5:
                            logging.warning(f"⚠️ 成交额数据缺失率过高: {amount_null_ratio:.1%}")
                        else:
                            logging.info(f"✅ 成交额数据质量良好，缺失率: {amount_null_ratio:.1%}")

                    # 批量获取复权因子数据
                    df_adj_factor = ts.pro_api().adj_factor(
                        ts_code=ts_codes_str,
                        start_date=s_date,
                        end_date=e_date,
                        fields='ts_code,trade_date,adj_factor'
                    )

                    # 批量获取每日指标数据
                    df_daily_basic = simple_api_retry(ts.pro_api().daily_basic, "daily_basic", max_retries=3,
                                                      ts_code=ts_codes_str,
                                                      start_date=s_date,
                                                      end_date=e_date,
                                                      fields='ts_code,trade_date,turnover_rate,turnover_rate_f,volume_ratio,pe,pe_ttm,pb,ps,ps_ttm,total_share,float_share,free_share,total_mv,circ_mv'
                                                      )

                    if df_daily is not None and not df_daily.empty:
                        # 合并数据
                        df = df_daily.copy()

                        # 合并复权因子数据
                        if df_adj_factor is not None and not df_adj_factor.empty:
                            df = pd.merge(df, df_adj_factor, on=['ts_code', 'trade_date'], how='left')
                        else:
                            df['adj_factor'] = 1.0  # 默认复权因子

                        # 合并每日基本指标数据
                        if df_daily_basic is not None and not df_daily_basic.empty:
                            df = pd.merge(df, df_daily_basic, on=['ts_code', 'trade_date'], how='left')
                        else:
                            logging.error(f'❌ 股票批次 {ts_codes_str} 基础指标数据获取失败')
                            continue

                        df_list.append(df)

                    time.sleep(0.5)  # 避免请求过于频繁

                if df_list:
                    # 合并所有时间区间的数据
                    df = pd.concat(df_list, ignore_index=True)
                    df = df.drop_duplicates(['ts_code', 'trade_date'])

                    # 处理复权因子的缺失值
                    if 'adj_factor' in df.columns:
                        df['adj_factor'] = df.groupby('ts_code')['adj_factor'].fillna(method='ffill').fillna(method='bfill')

                    # 向量化涨停判断
                    star_chinext_mask = df['ts_code'].str.startswith(('688', '300'))
                    limit_threshold = np.where(star_chinext_mask, 19.9, 9.9)
                    df['limit_up'] = df['pct_chg'] >= limit_threshold

                    # 缓存数据
                    for ts_code in ts_codes_str.split(','):
                        df_stock = df[df['ts_code'] == ts_code]
                        if not df_stock.empty:
                            cache_path = os.path.join(Config.CACHE_DIR, f'{ts_code}_qfq.csv')
                            df_stock.to_csv(cache_path, index=False)
                            logging.info(f'获取并缓存 {ts_code} 的数据')
                            data_list.append(df_stock)

                break  # 成功获取数据后跳出重试循环

            except Exception as e:
                logging.error(f'获取股票数据失败 (尝试 {attempt + 1}/{max_retries}): {e}')
                if attempt == max_retries - 1:
                    logging.error(f'多次尝试后仍无法获取数据: {ts_codes_str}')
                time.sleep(1)

    if data_list:
        return pd.concat(data_list, ignore_index=True)

    logging.error('获取数据失败')
    return pd.DataFrame()

def fetch_all_stock_data(stock_list, start_date, end_date):
    """获取所有股票的历史数据"""
    logging.info(f'开始获取所有股票数据，时间范围：{start_date}-{end_date}')

    # 获取所有股票代码
    if not stock_list:
        stock_list = simple_api_retry(ts.pro_api().stock_basic, "stock_basic", max_retries=3,
                                    exchange='', list_status='L')['ts_code'].tolist()

    # 批量获取数据
    all_data = pd.DataFrame()
    for i in range(0, len(stock_list), 100):  # 每次获取100只股票
        batch_codes = stock_list[i:i + 100]
        df = fetch_data(batch_codes, start_date, end_date)
        all_data = pd.concat([all_data, df], ignore_index=True)
        logging.info(f'已获取 {len(all_data)} 条记录')

    # 统计涨停数据
    limit_up_count = len(all_data[all_data['limit_up'] == True])
    logging.info(f'涨停记录总数: {limit_up_count}')

    return all_data

# ==================== 特征数据处理模块 ====================

def get_all_feature_columns(df):
    """获取所有特征列 - 使用全部特征，不进行限制"""
    if Config.USE_ALL_FEATURES:
        # 排除非特征列
        exclude_cols = ['ts_code', 'trade_date', 'stock_code', 'date', 'limit_up',
                       'next_day_limit_up', 'next_2_day_limit_up', 'next_day_return',
                       'next_2_day_return', 'name', 'industry', 'area', 'market']

        feature_cols = [col for col in df.columns if col not in exclude_cols]

        # 过滤掉包含目标变量信息的列
        target_keywords = ['target', 'label', 'y_', 'future', 'next_', 'tomorrow']
        feature_cols = [col for col in feature_cols
                       if not any(keyword in col.lower() for keyword in target_keywords)]

        logging.info(f"✅ 使用全部特征: {len(feature_cols)}个特征")
        return feature_cols
    else:
        # 如果不使用全部特征，返回核心特征
        core_features = ['pct_chg', 'change', 'open', 'high', 'low', 'close', 'pre_close',
                        'vol', 'amount', 'turnover_rate', 'volume_ratio', 'pe', 'pb',
                        'total_mv', 'circ_mv']
        available_features = [f for f in core_features if f in df.columns]
        logging.info(f"✅ 使用核心特征: {len(available_features)}个特征")
        return available_features

def add_technical_indicators(df):
    """添加技术指标"""
    try:
        # 确保数据按股票代码和日期排序
        df = df.sort_values(['ts_code', 'trade_date'])

        # 为每只股票计算技术指标
        for ts_code in df['ts_code'].unique():
            mask = df['ts_code'] == ts_code
            stock_data = df[mask].copy()

            if len(stock_data) < 20:  # 数据不足，跳过
                continue

            # 移动平均线
            stock_data['ma5'] = stock_data['close'].rolling(5).mean()
            stock_data['ma10'] = stock_data['close'].rolling(10).mean()
            stock_data['ma20'] = stock_data['close'].rolling(20).mean()

            # RSI
            if 'ta' in globals() and ta is not None:
                try:
                    stock_data['rsi'] = ta.RSI(stock_data['close'].values, timeperiod=14)

                    # MACD
                    macd, macd_signal, macd_hist = ta.MACD(stock_data['close'].values)
                    stock_data['macd'] = macd
                    stock_data['macd_signal'] = macd_signal
                    stock_data['macd_hist'] = macd_hist

                    # 布林带
                    upper, middle, lower = ta.BBANDS(stock_data['close'].values)
                    stock_data['bb_upper'] = upper
                    stock_data['bb_middle'] = middle
                    stock_data['bb_lower'] = lower

                except Exception as e:
                    logging.warning(f"计算TA-Lib指标失败: {e}")
            else:
                # 简化版RSI计算
                delta = stock_data['close'].diff()
                gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
                loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
                rs = gain / loss
                stock_data['rsi'] = 100 - (100 / (1 + rs))

            # 成交量相关指标
            stock_data['vol_ma5'] = stock_data['vol'].rolling(5).mean()
            stock_data['vol_ratio'] = stock_data['vol'] / stock_data['vol_ma5']

            # 价格相关指标
            stock_data['price_change_5d'] = stock_data['close'].pct_change(5) * 100
            stock_data['price_change_10d'] = stock_data['close'].pct_change(10) * 100

            # 振幅
            stock_data['amplitude'] = (stock_data['high'] - stock_data['low']) / stock_data['pre_close'] * 100

            # 更新原数据框
            df.loc[mask, stock_data.columns] = stock_data

        logging.info("✅ 技术指标计算完成")
        return df

    except Exception as e:
        logging.error(f"技术指标计算失败: {e}")
        return df

def add_market_features(df, market_index_data=None):
    """添加市场特征"""
    try:
        # 市场类型特征
        df['is_main_board'] = df['ts_code'].str.startswith(('000', '001', '002', '600', '601', '603'))
        df['is_chinext'] = df['ts_code'].str.startswith('300')
        df['is_star'] = df['ts_code'].str.startswith('688')
        df['is_bj'] = df['ts_code'].str.startswith(('430', '831', '832', '833', '834', '835', '836', '837', '838', '839'))

        # 如果有市场指数数据，添加相关特征
        if market_index_data is not None and not market_index_data.empty:
            # 这里可以添加与大盘相关的特征
            pass

        logging.info("✅ 市场特征添加完成")
        return df

    except Exception as e:
        logging.error(f"市场特征添加失败: {e}")
        return df

def preprocess_data_for_training(data, strategy_type):
    """为训练预处理数据 - 金融规则兼容的数据处理"""
    try:
        logging.info(f"开始预处理{strategy_type}策略数据")

        # 数据清洗
        data = data.dropna(subset=['ts_code', 'trade_date'])

        # 确保日期格式正确
        if 'trade_date' in data.columns:
            data['trade_date'] = pd.to_datetime(data['trade_date'], format='%Y%m%d', errors='coerce')

        # 添加技术指标
        data = add_technical_indicators(data)

        # 添加市场特征
        data = add_market_features(data)

        # 处理缺失值
        numeric_columns = data.select_dtypes(include=[np.number]).columns
        data[numeric_columns] = data[numeric_columns].fillna(0)

        # 创建目标变量
        data = create_target_variables(data, strategy_type)

        logging.info(f"✅ {strategy_type}策略数据预处理完成，数据形状: {data.shape}")
        return data

    except Exception as e:
        logging.error(f"数据预处理失败: {e}")
        return data

def create_target_variables(df, strategy_type):
    """创建目标变量"""
    try:
        # 确保数据按股票代码和日期排序
        df = df.sort_values(['ts_code', 'trade_date'])

        # 为每只股票创建目标变量
        for ts_code in df['ts_code'].unique():
            mask = df['ts_code'] == ts_code
            stock_data = df[mask].copy()

            # 次日涨跌幅
            stock_data['next_day_return'] = stock_data['pct_chg'].shift(-1)

            # 次日是否涨停
            if strategy_type == '首板':
                # 首板策略：次日涨停
                limit_threshold = 9.9 if not stock_data['ts_code'].iloc[0].startswith(('688', '300')) else 19.9
                stock_data['next_day_limit_up'] = (stock_data['next_day_return'] >= limit_threshold).astype(int)
            elif strategy_type == '连板':
                # 连板策略：连续涨停
                limit_threshold = 9.9 if not stock_data['ts_code'].iloc[0].startswith(('688', '300')) else 19.9
                stock_data['next_day_limit_up'] = (stock_data['next_day_return'] >= limit_threshold).astype(int)

            # 更新原数据框
            df.loc[mask, ['next_day_return', 'next_day_limit_up']] = stock_data[['next_day_return', 'next_day_limit_up']]

        # 移除最后一天的数据（没有次日数据）
        df = df.dropna(subset=['next_day_return', 'next_day_limit_up'])

        logging.info(f"✅ {strategy_type}策略目标变量创建完成")
        return df

    except Exception as e:
        logging.error(f"目标变量创建失败: {e}")
        return df

# ==================== 超参数调优模块 ====================

def get_default_hyperparameters(strategy_type='首板'):
    """获取默认超参数"""
    default_params = {
        '首板': {
            'lstm_units': 128,
            'attention_heads': 8,
            'dropout_rate': 0.3,
            'l2_reg': 0.001,
            'learning_rate': 0.001,
            'batch_size': 32,
            'epochs': 100,
            'patience': 15
        },
        '连板': {
            'lstm_units': 256,
            'attention_heads': 12,
            'dropout_rate': 0.4,
            'l2_reg': 0.0005,
            'learning_rate': 0.0005,
            'batch_size': 64,
            'epochs': 150,
            'patience': 20
        }
    }

    return default_params.get(strategy_type, default_params['首板'])

def optimize_hyperparameters(X_train, y_train, X_val, y_val, X_test, y_test,
                           n_trials=50, strategy_type='首板'):
    """使用Optuna进行超参数优化"""
    try:
        logging.info(f"开始{strategy_type}策略超参数优化，试验次数: {n_trials}")

        def objective(trial):
            # 定义超参数搜索空间
            params = {
                'lstm_units': trial.suggest_categorical('lstm_units', [64, 128, 256, 512]),
                'attention_heads': trial.suggest_categorical('attention_heads', [4, 8, 12, 16]),
                'dropout_rate': trial.suggest_float('dropout_rate', 0.1, 0.5),
                'l2_reg': trial.suggest_loguniform('l2_reg', 1e-5, 1e-2),
                'learning_rate': trial.suggest_loguniform('learning_rate', 1e-4, 1e-2),
                'batch_size': trial.suggest_categorical('batch_size', [16, 32, 64, 128])
            }

            try:
                # 构建模型
                model = build_model(
                    input_shape=(X_train.shape[1],),
                    **params
                )

                # 编译模型
                model.compile(
                    optimizer=Adam(learning_rate=params['learning_rate']),
                    loss='binary_crossentropy',
                    metrics=['accuracy', 'precision', 'recall']
                )

                # 训练模型
                history = model.fit(
                    X_train, y_train,
                    validation_data=(X_val, y_val),
                    batch_size=params['batch_size'],
                    epochs=50,  # 减少epochs以加快优化速度
                    verbose=0,
                    callbacks=[
                        EarlyStopping(patience=10, restore_best_weights=True)
                    ]
                )

                # 评估模型
                val_loss = min(history.history['val_loss'])

                return val_loss

            except Exception as e:
                logging.error(f"试验失败: {e}")
                return float('inf')

        # 创建研究对象
        study = optuna.create_study(direction='minimize')
        study.optimize(objective, n_trials=n_trials)

        # 获取最佳参数
        best_params = study.best_params
        logging.info(f"✅ 超参数优化完成，最佳参数: {best_params}")

        return best_params

    except Exception as e:
        logging.error(f"超参数优化失败: {e}")
        return get_default_hyperparameters(strategy_type)

def create_learning_rate_schedule(initial_lr=0.001, decay_steps=1000, decay_rate=0.9):
    """创建学习率调度"""
    def scheduler(epoch, lr):
        if epoch < 10:
            return initial_lr
        else:
            return initial_lr * (decay_rate ** (epoch // 10))

    return scheduler

def get_callbacks(strategy_type='首板', patience=15, initial_lr=0.001):
    """获取训练回调函数"""
    callbacks = [
        EarlyStopping(
            monitor='val_loss',
            patience=patience,
            restore_best_weights=True,
            verbose=1
        ),
        ReduceLROnPlateau(
            monitor='val_loss',
            factor=0.5,
            patience=patience//2,
            min_lr=1e-6,
            verbose=1
        ),
        ModelCheckpoint(
            filepath=os.path.join(Config.MODEL_DIR, f'best_model_{strategy_type}.h5'),
            monitor='val_loss',
            save_best_only=True,
            verbose=1
        )
    ]

    return callbacks

# ==================== 模型训练模块 ====================

def build_model(input_shape, lstm_units=128, attention_heads=8, dropout_rate=0.3, l2_reg=0.001):
    """构建深度学习模型"""
    try:
        # 输入层
        inputs = Input(shape=input_shape, name='input_layer')

        # 特征处理层
        x = Dense(256, activation='relu', kernel_regularizer=l2(l2_reg))(inputs)
        x = BatchNormalization()(x)
        x = Dropout(dropout_rate)(x)

        # LSTM层
        x = Reshape((-1, 1))(x)  # 为LSTM重塑输入
        x = LSTM(lstm_units, return_sequences=True, kernel_regularizer=l2(l2_reg))(x)
        x = Dropout(dropout_rate)(x)

        # 注意力机制
        attention = MultiHeadAttention(
            num_heads=attention_heads,
            key_dim=lstm_units // attention_heads
        )(x, x)

        # 残差连接
        x = Add()([x, attention])
        x = LayerNormalization()(x)

        # 全局池化
        x = GlobalAveragePooling1D()(x)

        # 分类头
        x = Dense(128, activation='relu', kernel_regularizer=l2(l2_reg))(x)
        x = Dropout(dropout_rate)(x)
        x = Dense(64, activation='relu', kernel_regularizer=l2(l2_reg))(x)
        x = Dropout(dropout_rate)(x)

        # 输出层
        classification_output = Dense(1, activation='sigmoid', name='classification_output')(x)
        regression_output = Dense(1, activation='linear', name='regression_output')(x)

        # 创建模型
        model = Model(inputs=inputs, outputs=[classification_output, regression_output])

        logging.info("✅ 模型构建完成")
        return model

    except Exception as e:
        logging.error(f"模型构建失败: {e}")
        raise

def compile_model(model, learning_rate=0.001, strategy_type='首板'):
    """编译模型"""
    try:
        # 定义损失函数权重
        loss_weights = {
            'classification_output': 1.0,
            'regression_output': 0.5
        }

        # 编译模型
        model.compile(
            optimizer=Adam(learning_rate=learning_rate),
            loss={
                'classification_output': 'binary_crossentropy',
                'regression_output': 'mse'
            },
            loss_weights=loss_weights,
            metrics={
                'classification_output': ['accuracy', 'precision', 'recall'],
                'regression_output': ['mae']
            }
        )

        logging.info(f"✅ {strategy_type}模型编译完成")
        return model

    except Exception as e:
        logging.error(f"模型编译失败: {e}")
        raise

def train_model(model, X_train, y_train, X_val, y_val, strategy_type='首板',
                batch_size=32, epochs=100, callbacks=None):
    """训练模型"""
    try:
        logging.info(f"开始训练{strategy_type}模型")

        # 准备目标变量
        if isinstance(y_train, pd.DataFrame):
            y_train_class = y_train['next_day_limit_up'].values
            y_train_reg = y_train['next_day_return'].values
        else:
            y_train_class = y_train
            y_train_reg = y_train

        if isinstance(y_val, pd.DataFrame):
            y_val_class = y_val['next_day_limit_up'].values
            y_val_reg = y_val['next_day_return'].values
        else:
            y_val_class = y_val
            y_val_reg = y_val

        # 训练模型
        history = model.fit(
            X_train,
            {
                'classification_output': y_train_class,
                'regression_output': y_train_reg
            },
            validation_data=(
                X_val,
                {
                    'classification_output': y_val_class,
                    'regression_output': y_val_reg
                }
            ),
            batch_size=batch_size,
            epochs=epochs,
            callbacks=callbacks,
            verbose=1
        )

        logging.info(f"✅ {strategy_type}模型训练完成")
        return model, history

    except Exception as e:
        logging.error(f"模型训练失败: {e}")
        raise

def evaluate_model(model, X_test, y_test, strategy_type='首板'):
    """评估模型性能"""
    try:
        logging.info(f"开始评估{strategy_type}模型")

        # 准备测试数据
        if isinstance(y_test, pd.DataFrame):
            y_test_class = y_test['next_day_limit_up'].values
            y_test_reg = y_test['next_day_return'].values
        else:
            y_test_class = y_test
            y_test_reg = y_test

        # 预测
        predictions = model.predict(X_test)
        pred_class = predictions[0].flatten()
        pred_reg = predictions[1].flatten()

        # 计算分类指标
        pred_class_binary = (pred_class > 0.5).astype(int)

        accuracy = accuracy_score(y_test_class, pred_class_binary)
        precision = precision_score(y_test_class, pred_class_binary)
        recall = recall_score(y_test_class, pred_class_binary)
        f1 = f1_score(y_test_class, pred_class_binary)

        # 计算AUC
        fpr, tpr, _ = roc_curve(y_test_class, pred_class)
        auc_score = auc(fpr, tpr)

        # 计算回归指标
        mae = mean_absolute_error(y_test_reg, pred_reg)
        r2 = r2_score(y_test_reg, pred_reg)

        # 记录结果
        results = {
            'accuracy': accuracy,
            'precision': precision,
            'recall': recall,
            'f1_score': f1,
            'auc': auc_score,
            'mae': mae,
            'r2_score': r2
        }

        logging.info(f"✅ {strategy_type}模型评估完成:")
        for metric, value in results.items():
            logging.info(f"  {metric}: {value:.4f}")

        return results

    except Exception as e:
        logging.error(f"模型评估失败: {e}")
        return {}

def save_model(model, strategy_type='首板', version='v1'):
    """保存模型"""
    try:
        model_path = os.path.join(Config.MODEL_DIR, f'model_{strategy_type}_{version}.h5')
        model.save(model_path)
        logging.info(f"✅ {strategy_type}模型已保存到: {model_path}")
        return model_path

    except Exception as e:
        logging.error(f"模型保存失败: {e}")
        return None

def load_model(strategy_type='首板', version='v1'):
    """加载模型"""
    try:
        model_path = os.path.join(Config.MODEL_DIR, f'model_{strategy_type}_{version}.h5')
        if os.path.exists(model_path):
            model = tf.keras.models.load_model(model_path)
            logging.info(f"✅ {strategy_type}模型已从 {model_path} 加载")
            return model
        else:
            logging.warning(f"模型文件不存在: {model_path}")
            return None

    except Exception as e:
        logging.error(f"模型加载失败: {e}")
        return None

# ==================== 元学习模块 ====================

def meta_learning_strategy_adaptation(models, X_new, strategy_type='首板'):
    """元学习策略适应"""
    try:
        logging.info(f"开始{strategy_type}元学习策略适应")

        # 简化的元学习实现
        # 这里可以添加更复杂的元学习算法

        predictions = []
        for model in models:
            if model is not None:
                pred = model.predict(X_new)
                predictions.append(pred)

        if predictions:
            # 简单的集成预测
            ensemble_pred = np.mean(predictions, axis=0)
            logging.info(f"✅ {strategy_type}元学习预测完成")
            return ensemble_pred
        else:
            logging.warning("没有可用的模型进行元学习")
            return None

    except Exception as e:
        logging.error(f"元学习失败: {e}")
        return None

# ==================== 知识迁移模块 ====================

def knowledge_transfer(source_model, target_data, strategy_type='首板'):
    """知识迁移"""
    try:
        logging.info(f"开始{strategy_type}知识迁移")

        # 简化的知识迁移实现
        # 这里可以添加更复杂的迁移学习算法

        if source_model is None:
            logging.warning("源模型为空，无法进行知识迁移")
            return None

        # 冻结部分层进行迁移学习
        for layer in source_model.layers[:-3]:  # 冻结除最后3层外的所有层
            layer.trainable = False

        logging.info(f"✅ {strategy_type}知识迁移设置完成")
        return source_model

    except Exception as e:
        logging.error(f"知识迁移失败: {e}")
        return None

# ==================== 预测模块 ====================

def predict_stocks(model, latest_data, strategy_type='首板', top_k=20):
    """预测股票并选择Top K"""
    try:
        logging.info(f"开始{strategy_type}股票预测")

        if model is None:
            logging.error("模型为空，无法进行预测")
            return pd.DataFrame()

        # 获取特征列
        feature_columns = get_all_feature_columns(latest_data)

        # 准备预测数据
        X_pred = latest_data[feature_columns].fillna(0)

        # 标准化数据
        scaler = StandardScaler()
        X_pred_scaled = scaler.fit_transform(X_pred)

        # 预测
        predictions = model.predict(X_pred_scaled)

        # 处理预测结果
        if isinstance(predictions, list):
            prob_pred = predictions[0].flatten()  # 分类概率
            return_pred = predictions[1].flatten()  # 回归预测
        else:
            prob_pred = predictions.flatten()
            return_pred = prob_pred

        # 创建结果DataFrame
        results = latest_data[['ts_code']].copy()
        results['probability'] = prob_pred
        results['predicted_return'] = return_pred
        results['strategy_type'] = strategy_type

        # 按概率排序并选择Top K
        results = results.sort_values('probability', ascending=False).head(top_k)

        logging.info(f"✅ {strategy_type}预测完成，选出{len(results)}只股票")
        return results

    except Exception as e:
        logging.error(f"股票预测失败: {e}")
        return pd.DataFrame()

def generate_trading_signals(predictions, threshold=0.6):
    """生成交易信号"""
    try:
        signals = predictions.copy()

        # 生成买入信号
        signals['buy_signal'] = signals['probability'] >= threshold

        # 生成风险评级
        signals['risk_level'] = pd.cut(
            signals['probability'],
            bins=[0, 0.3, 0.6, 0.8, 1.0],
            labels=['低', '中', '高', '极高']
        )

        # 生成推荐强度
        signals['recommendation'] = pd.cut(
            signals['probability'],
            bins=[0, 0.4, 0.6, 0.8, 1.0],
            labels=['观望', '关注', '买入', '强烈买入']
        )

        logging.info("✅ 交易信号生成完成")
        return signals

    except Exception as e:
        logging.error(f"交易信号生成失败: {e}")
        return predictions

# ==================== 循环主函数 ====================

def main_loop():
    """主循环函数"""
    try:
        logging.info("🚀 开始主循环")

        # 获取股票基础信息
        stock_basic = simple_api_retry(ts.pro_api().stock_basic, "stock_basic",
                                     exchange='', list_status='L')

        if stock_basic.empty:
            logging.error("无法获取股票基础信息")
            return

        stock_list = stock_basic['ts_code'].tolist()[:100]  # 限制股票数量用于测试

        # 获取历史数据
        logging.info("获取历史数据...")
        df = fetch_all_stock_data(stock_list, Config.START_DATE, Config.END_DATE)

        if df.empty:
            logging.error("无法获取历史数据")
            return

        # 数据预处理
        logging.info("数据预处理...")
        df_shouban = preprocess_data_for_training(df.copy(), '首板')
        df_lianban = preprocess_data_for_training(df.copy(), '连板')

        # 准备训练数据
        feature_columns = get_all_feature_columns(df_shouban)

        # 首板策略训练
        logging.info("训练首板策略模型...")
        X_shouban = df_shouban[feature_columns].fillna(0)
        y_shouban = df_shouban[['next_day_limit_up', 'next_day_return']].fillna(0)

        # 数据分割
        X_train, X_test, y_train, y_test = train_test_split(
            X_shouban, y_shouban, test_size=0.2, random_state=42
        )
        X_train, X_val, y_train, y_val = train_test_split(
            X_train, y_train, test_size=0.2, random_state=42
        )

        # 数据标准化
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_val_scaled = scaler.transform(X_val)
        X_test_scaled = scaler.transform(X_test)

        # 构建和训练首板模型
        model_shouban = build_model(input_shape=(X_train_scaled.shape[1],))
        model_shouban = compile_model(model_shouban, strategy_type='首板')

        callbacks = get_callbacks(strategy_type='首板')
        model_shouban, history_shouban = train_model(
            model_shouban, X_train_scaled, y_train, X_val_scaled, y_val,
            strategy_type='首板', callbacks=callbacks
        )

        # 评估首板模型
        results_shouban = evaluate_model(model_shouban, X_test_scaled, y_test, '首板')

        # 保存首板模型
        save_model(model_shouban, '首板')

        # 连板策略训练（类似流程）
        logging.info("训练连板策略模型...")
        X_lianban = df_lianban[feature_columns].fillna(0)
        y_lianban = df_lianban[['next_day_limit_up', 'next_day_return']].fillna(0)

        # 获取最新数据进行预测
        logging.info("获取最新数据进行预测...")
        latest_data = fetch_data(stock_list[:50],
                               (datetime.now() - timedelta(days=5)).strftime('%Y%m%d'),
                               datetime.now().strftime('%Y%m%d'))

        if not latest_data.empty:
            latest_data = add_technical_indicators(latest_data)
            latest_data = add_market_features(latest_data)

            # 首板预测
            shouban_predictions = predict_stocks(model_shouban, latest_data, '首板')
            shouban_signals = generate_trading_signals(shouban_predictions)

            logging.info("🎯 首板策略推荐:")
            for _, row in shouban_signals.head(10).iterrows():
                logging.info(f"  {row['ts_code']}: 概率={row['probability']:.3f}, "
                           f"预期收益={row['predicted_return']:.2f}%, "
                           f"推荐={row['recommendation']}")

        logging.info("✅ 主循环完成")

    except Exception as e:
        logging.error(f"主循环执行失败: {e}")
        raise

# ==================== 主函数 ====================

def main():
    """主函数"""
    try:
        # 设置日志
        logger = setup_logging()
        logger.info("🚀 股票涨停预测系统启动")

        # 显示配置信息
        logger.info(f"📊 系统配置:")
        logger.info(f"  - 数据范围: {Config.START_DATE} ~ {Config.END_DATE}")
        logger.info(f"  - 使用全部特征: {Config.USE_ALL_FEATURES}")
        logger.info(f"  - 模型目录: {Config.MODEL_DIR}")
        logger.info(f"  - 缓存目录: {Config.CACHE_DIR}")

        # 初始化Tushare
        if not hasattr(ts, 'pro_api') or ts.get_token() is None:
            logger.warning("⚠️ Tushare未配置，请设置token")
            logger.info("请在环境变量中设置TUSHARE_TOKEN或调用ts.set_token()")
            return

        # 执行主循环
        main_loop()

        logger.info("🎉 系统运行完成")

    except KeyboardInterrupt:
        logging.info("用户中断程序")
    except Exception as e:
        logging.error(f"系统运行失败: {e}")
        raise

if __name__ == "__main__":
    main()
