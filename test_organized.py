#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试整理后的P.py文件
"""

# 基础导入测试
import os
import sys
import warnings
import logging
from datetime import datetime, timedelta
from dataclasses import dataclass

# 科学计算库测试
import numpy as np
import pandas as pd

# 机器学习库测试
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score

print("🚀 基础导入测试通过")

# 测试配置类
@dataclass
class TestConfig:
    """测试配置类"""
    START_DATE: str = '2020-01-01'
    END_DATE: str = None
    USE_ALL_FEATURES: bool = True
    
    def __post_init__(self):
        if self.END_DATE is None:
            self.END_DATE = datetime.now().strftime('%Y-%m-%d')

# 测试配置
config = TestConfig()
print(f"📊 配置测试通过: {config.START_DATE} ~ {config.END_DATE}")
print(f"🎯 使用全部特征: {config.USE_ALL_FEATURES}")

# 测试日志配置
def setup_test_logging():
    """配置测试日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)

logger = setup_test_logging()
logger.info("✅ 日志系统测试通过")

# 测试数据处理函数
def test_data_processing():
    """测试数据处理功能"""
    # 创建测试数据
    dates = pd.date_range('2023-01-01', '2023-01-10', freq='D')
    test_data = pd.DataFrame({
        'date': dates,
        'ts_code': ['000001.SZ'] * len(dates),
        'open': np.random.uniform(10, 20, len(dates)),
        'high': np.random.uniform(15, 25, len(dates)),
        'low': np.random.uniform(8, 18, len(dates)),
        'close': np.random.uniform(12, 22, len(dates)),
        'volume': np.random.uniform(1000000, 10000000, len(dates)),
        'pct_chg': np.random.uniform(-5, 5, len(dates))
    })
    
    logger.info(f"测试数据创建完成，形状: {test_data.shape}")
    
    # 测试特征获取
    exclude_cols = ['ts_code', 'date', 'limit_up']
    feature_cols = [col for col in test_data.columns if col not in exclude_cols]
    logger.info(f"特征列数量: {len(feature_cols)}")
    logger.info(f"特征列: {feature_cols}")
    
    return test_data, feature_cols

# 运行测试
if __name__ == "__main__":
    logger.info("🧪 开始整理版代码测试")
    
    try:
        # 测试数据处理
        test_data, features = test_data_processing()
        
        # 测试基础机器学习功能
        X = test_data[features].fillna(0)
        y = (test_data['pct_chg'] > 5).astype(int)  # 简单的涨停标记
        
        # 数据分割测试
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42)
        logger.info(f"数据分割完成: 训练集{X_train.shape}, 测试集{X_test.shape}")
        
        # 数据标准化测试
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_test_scaled = scaler.transform(X_test)
        logger.info("数据标准化完成")
        
        logger.info("🎉 所有基础功能测试通过！")
        logger.info("✅ 整理后的代码结构正常，可以进行进一步开发")
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        raise
